import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, Home } from "lucide-react";
import { useLocation } from "wouter";
import ParticleBackground from "@/components/particle-background";

export default function NotFound() {
  const [, setLocation] = useLocation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      <ParticleBackground />

      <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
        <Card className="glass-card border-red-400/30 bg-gradient-to-br from-red-500/10 to-pink-600/10 rounded-2xl p-8 shadow-2xl relative overflow-hidden max-w-md w-full">
          <div className="absolute inset-0 bg-gradient-to-r from-red-400/5 to-pink-600/5 rounded-2xl"></div>
          <CardContent className="relative z-10 p-0 text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-400/20 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-400" />
              </div>
            </div>

            <h1 className="text-3xl font-bold text-white mb-4">404 - Page Not Found</h1>

            <p className="text-gray-300 mb-8 leading-relaxed">
              The page you're looking for doesn't exist or may have been moved.
              Let's get you back to discovering amazing startup ideas!
            </p>

            <Button
              onClick={() => setLocation('/dashboard')}
              className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white rounded-lg px-6 py-3 font-semibold transition-all duration-200"
            >
              <Home className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </CardContent>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-400/10 to-transparent rounded-full blur-3xl"></div>
        </Card>
      </div>
    </div>
  );
}
