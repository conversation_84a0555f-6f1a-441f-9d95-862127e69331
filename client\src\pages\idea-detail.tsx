import { useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, Share2, Bookmark, ExternalLink, AlertCircle, TrendingUp, Users, Calendar, Target, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth.tsx";
import { useFavoriteStatus, useToggleFavorite } from "@/hooks/use-favorites";
import { extractIdeaId, getIdeaShareUrl } from "@/lib/url-utils";
import SEOHead from "@/components/seo-head";
import SocialShare from "@/components/social-share";
import ParticleBackground from "@/components/particle-background";
import AuthModal from "@/components/auth-modal";
import type { StartupIdea } from "@/lib/types";

export default function IdeaDetailPage() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);

  const ideaId = extractIdeaId(params.id);

  const { data: idea, isLoading, error } = useQuery({
    queryKey: ['idea', ideaId],
    queryFn: async () => {
      const { supabase } = await import('@/lib/queryClient');
      const { data, error } = await supabase
        .from('startup_ideas')
        .select(`
          id,
          title,
          summary,
          industry_id,
          upvotes,
          comments,
          keywords,
          subreddit,
          reddit_post_urls,
          existing_solutions,
          solution_gaps,
          market_size,
          target_date,
          confidence_score,
          created_at,
          updated_at,
          industry:industries!industry_id(*)
        `)
        .eq('id', ideaId)
        .single();

      if (error) throw error;

      // Transform the data to match our TypeScript interface
      const transformedData: StartupIdea = {
        id: data.id,
        title: data.title,
        summary: data.summary,
        industryId: data.industry_id,
        upvotes: data.upvotes,
        comments: data.comments,
        keywords: data.keywords || [],
        subreddit: data.subreddit,
        redditPostUrls: data.reddit_post_urls || [],
        existingSolutions: data.existing_solutions,
        solutionGaps: data.solution_gaps,
        marketSize: data.market_size,
        targetDate: data.target_date,
        confidenceScore: data.confidence_score,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        industry: Array.isArray(data.industry) ? data.industry[0] : data.industry
      };

      return transformedData;
    },
    enabled: !!ideaId,
  });

  // Get favorite status for this idea
  const { data: favoriteStatus } = useFavoriteStatus(ideaId ? [ideaId] : []);
  const toggleFavoriteMutation = useToggleFavorite();

  const isFavorited = favoriteStatus?.[ideaId!] || false;

  // Redirect to 404 if idea not found
  useEffect(() => {
    if (error || (!isLoading && !idea)) {
      setLocation('/404');
    }
  }, [error, isLoading, idea, setLocation]);

  const handleBack = () => {
    setLocation('/dashboard');
  };

  const handleShare = async () => {
    if (!idea) return;

    const shareUrl = getIdeaShareUrl(idea.id, idea.title);

    // Always copy to clipboard for simplicity
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: "Link copied!",
        description: "Share URL has been copied to your clipboard. Anyone can view this link.",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard.",
        variant: "destructive",
      });
    }
  };

  const handleFavoriteClick = async () => {
    if (!user) {
      setAuthModalOpen(true);
      return;
    }

    if (!ideaId) return;

    try {
      await toggleFavoriteMutation.mutateAsync({ ideaId, isFavorited });
      toast({
        title: isFavorited ? "Removed from favorites" : "Added to favorites",
        description: isFavorited ? "Idea removed from your favorites" : "Idea saved to your favorites",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update favorite status. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!ideaId) {
    setLocation('/404');
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        <ParticleBackground />
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10 rounded-lg bg-white/10" />
              <Skeleton className="h-6 w-20 bg-white/10" />
            </div>
            <Skeleton className="h-12 w-3/4 bg-white/10" />
            <Skeleton className="h-64 w-full bg-white/10" />
          </div>
        </div>
      </div>
    );
  }

  if (!idea) {
    return null; // Will redirect to 404
  }

  return (
    <>
      <SEOHead idea={idea} type="article" />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        <ParticleBackground />
        
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            
            {/* Header Navigation */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-between mb-8"
            >
              <Button
                onClick={handleBack}
                className="glass-card rounded-lg px-4 py-2 text-white hover:bg-white/20 transition-all duration-200 border border-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              
              <div className="flex items-center space-x-3">
                <Button
                  onClick={handleShare}
                  className="glass-card rounded-lg px-4 py-2 text-white hover:bg-white/20 transition-all duration-200 border border-white/20"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>

                <Button
                  onClick={handleFavoriteClick}
                  className={`px-4 py-2 rounded-lg font-semibold transition-all duration-200 ${
                    isFavorited
                      ? 'bg-pink-500/20 hover:bg-pink-500/30 text-pink-400 border border-pink-400/30'
                      : 'glass-card hover:bg-pink-500/20 hover:text-pink-400 text-white border border-white/20'
                  }`}
                  disabled={toggleFavoriteMutation.isPending}
                >
                  <Bookmark className="w-4 h-4 mr-2" />
                  {isFavorited ? "Saved" : "Save"}
                </Button>
              </div>
            </motion.div>

            {/* Hero Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="mb-8"
            >
              <Card className="glass-card border-cyan-400/30 bg-gradient-to-br from-cyan-500/10 to-blue-600/10 rounded-2xl p-8 shadow-2xl relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/5 to-blue-600/5 rounded-2xl"></div>
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <Badge
                      className="px-4 py-2 rounded-full text-base font-medium"
                      style={{ 
                        backgroundColor: `${(idea.industry as any)?.color || '#6b7280'}20`, 
                        color: (idea.industry as any)?.color || '#6b7280',
                        border: `1px solid ${(idea.industry as any)?.color || '#6b7280'}40`
                      }}
                    >
                      {(idea.industry as any)?.name || 'Uncategorized'}
                    </Badge>
                    
                    <div className="flex items-center flex-wrap gap-x-2 gap-y-1 text-gray-300 text-sm font-medium">
                      <span className="flex items-center space-x-1.5">
                        <TrendingUp className="w-4 h-4 text-green-400" />
                        <span>{idea.upvotes} upvotes</span>
                      </span>
                      <span className="flex items-center space-x-1.5">
                        <Users className="w-4 h-4 text-blue-400" />
                        <span>{idea.comments} comments</span>
                      </span>
                      <span className="flex items-center space-x-1.5">
                        <Target className="w-4 h-4 text-yellow-400" />
                        <span>{idea.confidenceScore || 0}% confidence</span>
                      </span>
                      <span className="flex items-center space-x-1.5">
                        <MessageCircle className="w-4 h-4 text-orange-500" />
                        <span>r/{idea.subreddit}</span>
                      </span>
                      <span className="flex items-center space-x-1.5">
                        <Calendar className="w-4 h-4 text-purple-400" />
                        <span>{idea.targetDate ? new Date(idea.targetDate).toLocaleDateString('en-US') : 'Unknown date'}</span>
                      </span>
                    </div>
                  </div>
                  
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                    {idea.title}
                  </h1>
                  
                  <div className="flex flex-wrap gap-2 mb-6">
                    {idea.keywords && idea.keywords.length > 0 && (
                      idea.keywords.map((keyword: string, index: number) => (
                        <Badge key={index} className="bg-cyan-400/20 text-cyan-400 px-3 py-1 rounded-full text-base font-medium border border-cyan-400/30">
                          {keyword}
                        </Badge>
                      ))
                    )}
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-cyan-400/10 to-transparent rounded-full blur-3xl"></div>
              </Card>
            </motion.div>

            {/* Problem Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-8"
            >
              <Card className="glass-card border-orange-400/30 bg-gradient-to-br from-orange-500/10 to-red-600/10 rounded-2xl p-8 shadow-2xl relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400/5 to-red-600/5 rounded-2xl"></div>
                <div className="relative z-10">
                  <div className="flex items-center mb-6">
                    <div className="w-3 h-3 bg-orange-400 rounded-full mr-4 shadow-lg shadow-orange-400/50"></div>
                    <h2 className="text-3xl font-bold text-white">App Idea</h2>
                    <div className="ml-4 px-3 py-1 bg-orange-400/20 rounded-full border border-orange-400/30">
                      <span className="text-orange-400 text-base font-medium">Startup Concept</span>
                    </div>
                  </div>
                  <p className="text-gray-200 leading-relaxed text-lg font-medium">{idea.summary}</p>
                </div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-400/10 to-transparent rounded-full blur-3xl"></div>
              </Card>
            </motion.div>

            {/* Pain Points Section */}
            {idea.solutionGaps && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mb-8"
              >
                <Card className="glass-card border-red-400/30 bg-gradient-to-br from-red-500/10 to-pink-600/10 rounded-2xl p-8 shadow-2xl relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-red-400/5 to-pink-600/5 rounded-2xl"></div>
                  <div className="relative z-10">
                    <div className="flex items-center mb-6">
                      <div className="w-3 h-3 bg-red-400 rounded-full mr-4 shadow-lg shadow-red-400/50"></div>
                      <h2 className="text-3xl font-bold text-white">Key Pain Points</h2>
                      <div className="ml-4 px-3 py-1 bg-red-400/20 rounded-full border border-red-400/30">
                        <span className="text-red-400 text-base font-medium">Critical Issues</span>
                      </div>
                    </div>
                    <p className="text-gray-200 leading-relaxed text-lg font-medium">{idea.solutionGaps}</p>
                  </div>
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-400/10 to-transparent rounded-full blur-3xl"></div>
                </Card>
              </motion.div>
            )}

            {/* Market Opportunity & Solutions Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"
            >
              {/* Market Size */}
              <Card className="glass-card border-green-400/20 bg-gradient-to-br from-green-500/5 to-emerald-600/5 rounded-xl p-6">
                <CardContent className="p-0">
                  <div className="flex items-center mb-4">
                    <Target className="w-6 h-6 text-green-400 mr-3" />
                    <h3 className="text-xl font-semibold text-white">Market Opportunity</h3>
                  </div>
                  <p className="text-gray-200 leading-relaxed text-lg font-medium">
                    {idea.marketSize || 'Market size analysis not available'}
                  </p>
                </CardContent>
              </Card>

              {/* Existing Solutions */}
              {idea.existingSolutions && (
                <Card className="glass-card border-purple-400/20 bg-gradient-to-br from-purple-500/5 to-violet-600/5 rounded-xl p-6">
                  <CardContent className="p-0">
                    <div className="flex items-center mb-4">
                      <ExternalLink className="w-6 h-6 text-purple-400 mr-3" />
                      <h3 className="text-xl font-semibold text-white">Current Solutions</h3>
                    </div>
                    <p className="text-gray-200 leading-relaxed text-lg font-medium">{idea.existingSolutions}</p>
                  </CardContent>
                </Card>
              )}
            </motion.div>

            {/* Source Discussions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="mb-8"
            >
              <Card className="glass-card border-orange-400/20 bg-gradient-to-br from-orange-500/5 to-red-600/5 rounded-xl p-6">
                <CardContent className="p-0">
                  <div className="flex items-center mb-6">
                    <i className="fab fa-reddit text-orange-500 mr-3 text-xl"></i>
                    <h3 className="text-2xl font-bold text-white">Source Discussions</h3>
                    <div className="ml-4 px-3 py-1 bg-orange-400/20 rounded-full border border-orange-400/30">
                      <span className="text-orange-400 text-base font-medium">{idea.redditPostUrls?.length || 0} Posts</span>
                    </div>
                  </div>

                  {idea.redditPostUrls && idea.redditPostUrls.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {idea.redditPostUrls.map((url: string, index: number) => (
                        <a
                          key={index}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 hover:border-orange-400/30 transition-all duration-200 group"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-orange-400 font-medium text-base">r/{idea.subreddit}</span>
                            <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-orange-400 transition-colors" />
                          </div>
                          <p className="text-gray-300 text-base font-medium">Discussion #{index + 1}</p>
                        </a>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 bg-white/5 rounded-lg border border-white/10 text-center">
                      <p className="text-gray-400 text-base font-medium">No source links available</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Social Share Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mb-8"
            >
              <SocialShare idea={idea} />
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="text-center"
            >
              <Card className="glass-card border-white/20 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl p-8">
                <CardContent className="p-0">
                  <h3 className="text-2xl font-bold text-white mb-4">Ready to Build This Idea?</h3>
                  <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
                    This startup opportunity was discovered through AI analysis of Reddit discussions.
                    Join thousands of entrepreneurs who use IdeaHunter to find their next big idea.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      onClick={handleFavoriteClick}
                      className="glass-card rounded-lg px-6 py-3 text-white hover:bg-white/20 transition-all duration-200 border border-white/20"
                    >
                      <Bookmark className="w-4 h-4 mr-2" />
                      Save Idea
                    </Button>
                    <Button
                      onClick={() => setLocation('/dashboard')}
                      className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white rounded-lg px-6 py-3 font-semibold transition-all duration-200"
                    >
                      Discover More Ideas
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

          </div>
        </div>
      </div>

      {authModalOpen && <AuthModal open={authModalOpen} onOpenChange={setAuthModalOpen} />}
    </>
  );
}
